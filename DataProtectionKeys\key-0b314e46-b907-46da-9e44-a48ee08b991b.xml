﻿<?xml version="1.0" encoding="utf-8"?>
<key id="0b314e46-b907-46da-9e44-a48ee08b991b" version="1">
  <creationDate>2025-08-13T10:34:53.185101Z</creationDate>
  <activationDate>2025-08-13T10:34:53.1570573Z</activationDate>
  <expirationDate>2025-11-11T10:34:53.1570573Z</expirationDate>
  <descriptor deserializerType="Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60">
    <descriptor>
      <encryption algorithm="AES_256_CBC" />
      <validation algorithm="HMACSHA256" />
      <masterKey p4:requiresEncryption="true" xmlns:p4="http://schemas.asp.net/2015/03/dataProtection">
        <!-- Warning: the key below is in an unencrypted form. -->
        <value>GUDl3Hyf5RkLCipS+sOtstfsgFGyqm+kMxyfSP8qtZnh3ZJKqsJMKqV5x4G0tfrFe8vq7lzYiZX0zIF+UiKIyg==</value>
      </masterKey>
    </descriptor>
  </descriptor>
</key>