using Azure.Core;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json.Linq;
using StudentManagementAPI.Configuration;
using StudentManagementAPI.Models;
using StudentManagementAPI.service;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IdentityModel.Tokens.Jwt;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Reflection;
using System.Security.Claims;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using Twilio.Http;
using static StudentManagementAPI.Controllers.SalesforceUsersController;

namespace StudentManagementAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly IConfiguration _configuration;
        private readonly Data.ApplicationDbContext _context; // Add ApplicationDbContext
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ILogger<AuthController> _logger;
        private readonly IOptions<SalesforceSettings> _salesforceSettings;
        private readonly RsaHelper _uniService1;
        private readonly IConfiguration _config;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public AuthController(
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            IConfiguration configuration,
            Data.ApplicationDbContext context,
            getuni2023 uniService,
            RsaHelper uniService1,
            IHttpClientFactory httpClientFactory,
            IOptions<SalesforceSettings> salesforceSettings,
            IConfiguration config,
            IHttpContextAccessor httpContextAccessor,
            ILogger<AuthController> logger)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _configuration = configuration;
            _context = context; // Assign injected context
            _uniService = uniService;
            _uniService1 = uniService1;
            _httpClientFactory = httpClientFactory;
            _salesforceSettings = salesforceSettings ?? throw new ArgumentNullException(nameof(salesforceSettings));
            _config = config;
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
            _httpClient = httpClientFactory.CreateClient();
        }
        // Social login endpoints - Direct OAuth for API
        [HttpGet("~/google-login")]
        public IActionResult GoogleLogin()
        {
            try
            {
                // Get return URL from query parameter or default to frontend callback
                var returnUrl = Request.Query["returnUrl"].FirstOrDefault() ?? "https://unitededucation.com/auth/callback";
                _logger.LogInformation("Return URL: {ReturnUrl}", returnUrl);

                // Get Google OAuth configuration
                var googleClientId = _configuration["Authentication:Google:ClientId"] ?? throw new InvalidOperationException("Google ClientId not configured");
                var redirectUri = $"{Request.Scheme}://{Request.Host}/external-login-callback";

                _logger.LogInformation("Google ClientId configured: {HasClientId}", !string.IsNullOrEmpty(googleClientId));
                _logger.LogInformation("Redirect URI: {RedirectUri}", redirectUri);

                // Build Google OAuth URL directly (without state parameter for API)
                var googleAuthUrl = "https://accounts.google.com/o/oauth2/v2/auth" +
                    $"?client_id={Uri.EscapeDataString(googleClientId)}" +
                    $"&redirect_uri={Uri.EscapeDataString(redirectUri)}" +
                    $"&response_type=code" +
                    $"&scope={Uri.EscapeDataString("openid email profile")}" +
                    $"&access_type=offline";

                _logger.LogInformation("Redirecting to Google OAuth URL: {GoogleAuthUrl}", googleAuthUrl);

                return Redirect(googleAuthUrl);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initiating Google login");
                return BadRequest(new { error = "Failed to initiate Google login", details = ex.Message });
            }
        }
        [HttpGet("~/signin-facebook")]
        public IActionResult SignInWithFacebook(string? returnUrl = null)
        {
            var redirectUrl = Url.Action(nameof(ExternalLoginCallback), "Auth", new { returnUrl });
            var properties = _signInManager.ConfigureExternalAuthenticationProperties("Facebook", redirectUrl);
            return Challenge(properties, "Facebook");
        }

        [HttpGet("~/signin-microsoft")]
        public IActionResult SignInWithMicrosoft(string? returnUrl = null)
        {
            var redirectUrl = Url.Action(nameof(ExternalLoginCallback), "Auth", new { returnUrl });
            var properties = _signInManager.ConfigureExternalAuthenticationProperties("Microsoft", redirectUrl);
            return Challenge(properties, "Microsoft");
        }

        [HttpGet("~/signin-apple")]
        public IActionResult SignInWithApple(string? returnUrl = null)
        {
            var redirectUrl = Url.Action(nameof(ExternalLoginCallback), "Auth", new { returnUrl });
            var properties = _signInManager.ConfigureExternalAuthenticationProperties("Apple", redirectUrl);
            return Challenge(properties, "Apple");
        }

        [HttpGet("~/external-login-callback")]
        public async Task<IActionResult> ExternalLoginCallback(string? code = null, string? error = null, string? returnUrl = null)
        {
            try
            {
                _logger.LogInformation("External login callback received. Code: {Code}, Error: {Error}, ReturnUrl: {ReturnUrl}",
                    !string.IsNullOrEmpty(code) ? "Present" : "Missing", error, returnUrl);
                _logger.LogInformation("Request URL: {RequestUrl}", Request.GetDisplayUrl());
                _logger.LogInformation("Query String: {QueryString}", Request.QueryString);

                returnUrl = returnUrl ?? "https://unitededucation.com/auth/callback";

                if (!string.IsNullOrEmpty(error))
                {
                    _logger.LogError($"Error from Google OAuth: {error}");
                    return Redirect($"{returnUrl}?error={Uri.EscapeDataString(error)}");
                }

                if (string.IsNullOrEmpty(code))
                {
                    _logger.LogError("No authorization code received from Google");
                    return Redirect($"{returnUrl}?error=No authorization code received");
                }

                // Exchange authorization code for access token
                _logger.LogInformation("Exchanging authorization code for access token...");
                var tokenResponse = await ExchangeCodeForTokenAsync(code);

                if (tokenResponse == null)
                {
                    _logger.LogError("Failed to exchange authorization code for access token");
                    return Redirect($"{returnUrl}?error=Failed to get access token");
                }

                // Get user info from Google
                _logger.LogInformation("Getting user info from Google...");
                var userInfo = await GetGoogleUserInfoAsync(tokenResponse.AccessToken);

                if (userInfo == null)
                {
                    _logger.LogError("Failed to get user info from Google");
                    return Redirect($"{returnUrl}?error=Failed to get user info");
                }

                _logger.LogInformation("Google user info received. Email: {Email}, Name: {Name}", userInfo.Email, userInfo.Name);

                // Try to find existing user by Google ID or email
                var existingUser = await _userManager.FindByLoginAsync("Google", userInfo.Id);
                if (existingUser != null)
                {
                    _logger.LogInformation("Existing user found, generating JWT token");
                    var token = await GenerateJwtToken(existingUser);
                    return Redirect($"{returnUrl}?token={Uri.EscapeDataString(token)}");
                }

                // Check if user exists by email
                existingUser = await _userManager.FindByEmailAsync(userInfo.Email);
                if (existingUser != null)
                {
                    _logger.LogInformation("User exists with email, adding Google login");
                    var addLoginResult = await _userManager.AddLoginAsync(existingUser, new UserLoginInfo("Google", userInfo.Id, "Google"));
                    if (addLoginResult.Succeeded)
                    {
                        var token = await GenerateJwtToken(existingUser);
                        return Redirect($"{returnUrl}?token={Uri.EscapeDataString(token)}");
                    }
                    else
                    {
                        _logger.LogError("Failed to add Google login to existing user: {Errors}",
                            string.Join(", ", addLoginResult.Errors.Select(e => e.Description)));
                        return Redirect($"{returnUrl}?error=Failed to link Google account");
                    }
                }

                // Create new user
                _logger.LogInformation("Creating new user with Google account");

                // Split the name into first and last name
                var nameParts = userInfo.Name?.Split(new[] { ' ' }, 2, StringSplitOptions.RemoveEmptyEntries) ?? new string[0];
                var firstName = nameParts.Length > 0 ? nameParts[0] : "";
                var lastName = nameParts.Length > 1 ? nameParts[1] : "";

                var newUser = new ApplicationUser
                {
                    UserName = userInfo.Email,
                    Email = userInfo.Email,
                    EmailConfirmed = userInfo.VerifiedEmail,
                    FirstName = firstName,
                    LastName = lastName
                };

                var createResult = await _userManager.CreateAsync(newUser);
                if (createResult.Succeeded)
                {
                    _logger.LogInformation("New user created successfully");

                    // Add Google login to the new user
                    var addLoginResult = await _userManager.AddLoginAsync(newUser, new UserLoginInfo("Google", userInfo.Id, "Google"));
                    if (addLoginResult.Succeeded)
                    {
                        _logger.LogInformation("Google login added to new user");
                        var token = await GenerateJwtToken(newUser);
                        return Redirect($"{returnUrl}?token={Uri.EscapeDataString(token)}");
                    }
                    else
                    {
                        _logger.LogError("Failed to add Google login to new user: {Errors}",
                            string.Join(", ", addLoginResult.Errors.Select(e => e.Description)));
                        return Redirect($"{returnUrl}?error=Failed to create Google account");
                    }
                }
                else
                {
                    _logger.LogError("Failed to create new user: {Errors}",
                        string.Join(", ", createResult.Errors.Select(e => e.Description)));
                    return Redirect($"{returnUrl}?error=Failed to create user account");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception occurred in external login callback");
                var fallbackReturnUrl = returnUrl ?? "https://unitededucation.com/auth/callback";
                return Redirect($"{fallbackReturnUrl}?error={Uri.EscapeDataString("Internal server error during authentication")}");
            }
        }

        private async Task<string> GenerateJwtToken(ApplicationUser user)
        {
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id),
                new Claim(ClaimTypes.Name, user.UserName ?? string.Empty),
                new Claim(ClaimTypes.Email, user.Email ?? string.Empty),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
            };

            var roles = await _userManager.GetRolesAsync(user);
            claims.AddRange(roles.Select(role => new Claim(ClaimTypes.Role, role)));

            var jwtKey = _config["Jwt:Key"];
            if (string.IsNullOrEmpty(jwtKey))
                throw new InvalidOperationException("JWT Key is not configured");

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtKey));
            var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var durationInDaysStr = _config["Jwt:DurationInDays"];
            if (!double.TryParse(durationInDaysStr, out double durationInDays))
                durationInDays = 7; // Default to 7 days

            var expires = DateTime.Now.AddDays(durationInDays);

            var token = new JwtSecurityToken(
                _config["Jwt:Issuer"],
                _config["Jwt:Audience"],
                claims,
                expires: expires,
                signingCredentials: credentials
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }

        // Test endpoint to check authentication configuration
        [HttpGet("test-auth-config")]
        public IActionResult TestAuthConfig()
        {
            var googleClientId = _configuration["Authentication:Google:ClientId"];
            var googleClientSecret = _configuration["Authentication:Google:ClientSecret"];
            var facebookAppId = _configuration["Authentication:Facebook:AppId"];
            var microsoftClientId = _configuration["Authentication:Microsoft:ClientId"];

            return Ok(new
            {
                GoogleConfigured = !string.IsNullOrEmpty(googleClientId),
                GoogleClientIdPresent = !string.IsNullOrEmpty(googleClientId),
                GoogleClientSecretPresent = !string.IsNullOrEmpty(googleClientSecret),
                FacebookConfigured = !string.IsNullOrEmpty(facebookAppId),
                MicrosoftConfigured = !string.IsNullOrEmpty(microsoftClientId),
                CallbackUrl = "/external-login-callback"
            });
        }

        // Simple test callback to debug the 500 error
        [HttpGet("~/external-login-callback-test")]
        public async Task<IActionResult> ExternalLoginCallbackTest(string? returnUrl = null, string? remoteError = null)
        {
            try
            {
                _logger.LogInformation("=== TEST CALLBACK START ===");
                _logger.LogInformation("Query parameters: {QueryString}", Request.QueryString);

                return Ok(new
                {
                    message = "Test callback reached successfully",
                    returnUrl = returnUrl,
                    remoteError = remoteError,
                    queryString = Request.QueryString.ToString(),
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in test callback");
                return StatusCode(500, new { error = ex.Message, stackTrace = ex.StackTrace });
            }
        }

        private readonly getuni2023 _uniService;
        private readonly System.Net.Http.HttpClient _httpClient;

        // Helper methods for direct Google OAuth
        private async Task<GoogleTokenResponse?> ExchangeCodeForTokenAsync(string code)
        {
            try
            {
                var clientId = _configuration["Authentication:Google:ClientId"] ?? throw new InvalidOperationException("Google ClientId not configured");
                var clientSecret = _configuration["Authentication:Google:ClientSecret"] ?? throw new InvalidOperationException("Google ClientSecret not configured");
                var redirectUri = $"{Request.Scheme}://{Request.Host}/external-login-callback";

                var tokenRequest = new Dictionary<string, string>
                {
                    {"code", code},
                    {"client_id", clientId},
                    {"client_secret", clientSecret},
                    {"redirect_uri", redirectUri},
                    {"grant_type", "authorization_code"}
                };

                var content = new FormUrlEncodedContent(tokenRequest);
                var response = await _httpClient.PostAsync("https://oauth2.googleapis.com/token", content);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Token exchange failed: {StatusCode} - {Content}", response.StatusCode, errorContent);
                    return null;
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<GoogleTokenResponse>(responseContent, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exchanging code for token");
                return null;
            }
        }

        private async Task<GoogleUserInfo?> GetGoogleUserInfoAsync(string accessToken)
        {
            try
            {
                var request = new HttpRequestMessage(System.Net.Http.HttpMethod.Get, "https://www.googleapis.com/oauth2/v2/userinfo");
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(request);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("User info request failed: {StatusCode} - {Content}", response.StatusCode, errorContent);
                    return null;
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<GoogleUserInfo>(responseContent, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user info from Google");
                return null;
            }
        }

        /// <summary>
        /// Extracts the numeric phone (without country code) and the ISO country code.
        /// </summary>
        private (string PhoneWithoutCountryCode, string CountryCode) ExtractCountryCode(string phoneNumber)
        {
            if (string.IsNullOrEmpty(phoneNumber))
                return (phoneNumber, string.Empty);

            // Remove leading '+' if present
            if (phoneNumber.StartsWith("+"))
                phoneNumber = phoneNumber.Substring(1);

            // Keep digits only
            var digitsOnly = new string(phoneNumber.Where(char.IsDigit).ToArray());

            // List of country calling codes and ISO codes
            var countryCodes = new List<KeyValuePair<string, string>>
        {
            new("998", "UZ"), new("996", "KG"), new("995", "GE"), new("994", "AZ"),
            new("993", "TM"), new("992", "TJ"), new("976", "MN"), new("975", "BT"),
            new("974", "QA"), new("973", "BH"), new("972", "IL"), new("971", "AE"),
            new("970", "PS"), new("968", "OM"), new("967", "YE"), new("966", "SA"),
            new("965", "KW"), new("964", "IQ"), new("963", "SY"), new("962", "JO"),
            new("961", "LB"), new("960", "MV"), new("959", "MM"), new("958", "LK"),
            new("994", "AZ"), new("93", "AF"), new("355", "AL"), new("213", "DZ"),
            new("1",   "US"), new("376", "AD"), new("244", "AO"), new("1",   "AG"),
            new("54",  "AR"), new("374", "AM"), new("297", "AW"), new("61",  "AU"),
            new("43",  "AT"), new("994", "AZ"), new("1",   "BS"), new("973", "BH"),
            new("880", "BD"), new("1",   "BB"), new("375", "BY"), new("32",  "BE"),
            new("501", "BZ"), new("229", "BJ"), new("1",   "BM"), new("975", "BT"),
            new("591", "BO"), new("387", "BA"), new("267", "BW"), new("55",  "BR"),
            new("673", "BN"), new("359", "BG"), new("226", "BF"), new("257", "BI"),
            new("855", "KH"), new("237", "CM"), new("1",   "CA"), new("238", "CV"),
            new("1",   "KY"), new("236", "CF"), new("235", "TD"), new("56",  "CL"),
            new("86",  "CN"), new("57",  "CO"), new("269", "KM"), new("242", "CG"),
            new("243", "CD"), new("682", "CK"), new("506", "CR"), new("385", "HR"),
            new("53",  "CU"), new("357", "CY"), new("420", "CZ"), new("45",  "DK"),
            new("253", "DJ"), new("1",   "DM"), new("1",   "DO"), new("670", "TL"),
            new("593", "EC"), new("20",  "EG"), new("503", "SV"), new("240", "GQ"),
            new("291", "ER"), new("372", "EE"), new("251", "ET"), new("500", "FK"),
            new("298", "FO"), new("679", "FJ"), new("358", "FI"), new("33",  "FR"),
            new("594", "GF"), new("689", "PF"), new("241", "GA"), new("220", "GM"),
            new("995", "GE"), new("49",  "DE"), new("233", "GH"), new("350", "GI"),
            new("30",  "GR"), new("299", "GL"), new("1",   "GD"), new("590", "GP"),
            new("1",   "GU"), new("502", "GT"), new("224", "GN"), new("245", "GW"),
            new("592", "GY"), new("509", "HT"), new("504", "HN"), new("852", "HK"),
            new("36",  "HU"), new("354", "IS"), new("91",  "IN"), new("62",  "ID"),
            new("98",  "IR"), new("964", "IQ"), new("353", "IE"), new("972", "IL"),
            new("39",  "IT"), new("1",   "JM"), new("81",  "JP"), new("962", "JO"),
            new("7",   "KZ"), new("254", "KE"), new("686", "KI"), new("383", "XK"),
            new("965", "KW"), new("996", "KG"), new("856", "LA"), new("371", "LV"),
            new("961", "LB"), new("266", "LS"), new("231", "LR"), new("218", "LY"),
            new("423", "LI"), new("370", "LT"), new("352", "LU"), new("853", "MO"),
            new("389", "MK"), new("261", "MG"), new("265", "MW"), new("60",  "MY"),
            new("960", "MV"), new("223", "ML"), new("356", "MT"), new("692", "MH"),
            new("596", "MQ"), new("222", "MR"), new("230", "MU"), new("262", "YT"),
            new("52",  "MX"), new("691", "FM"), new("373", "MD"), new("377", "MC"),
            new("976", "MN"), new("382", "ME"), new("1",   "MS"), new("212", "MA"),
            new("258", "MZ"), new("95",  "MM"), new("264", "NA"), new("674", "NR"),
            new("977", "NP"), new("31",  "NL"), new("599", "AN"), new("687", "NC"),
            new("64",  "NZ"), new("505", "NI"), new("227", "NE"), new("234", "NG"),
            new("683", "NU"), new("850", "KP"), new("1",   "MP"), new("47",  "NO"),
            new("968", "OM"), new("92",  "PK"), new("680", "PW"), new("507", "PA"),
            new("675", "PG"), new("595", "PY"), new("51",  "PE"), new("63",  "PH"),
            new("48",  "PL"), new("351", "PT"), new("1",   "PR"), new("974", "QA"),
            new("242", "RE"), new("40",  "RO"), new("7",   "RU"), new("250", "RW"),
            new("590", "BL"), new("290", "SH"), new("1",   "KN"), new("1",   "LC"),
            new("590", "MF"), new("508", "PM"), new("1",   "VC"), new("685", "WS"),
            new("378", "SM"), new("239", "ST"), new("966", "SA"), new("221", "SN"),
            new("381", "RS"), new("248", "SC"), new("232", "SL"), new("65",  "SG"),
            new("1",   "SX"), new("421", "SK"), new("386", "SI"), new("677", "SB"),
            new("252", "SO"), new("27",  "ZA"), new("82",  "KR"), new("211", "SS"),
            new("34",  "ES"), new("94",  "LK"), new("249", "SD"), new("597", "SR"),
            new("47",  "SJ"), new("268", "SZ"), new("46",  "SE"), new("41",  "CH"),
            new("963", "SY"), new("886", "TW"), new("255", "TZ"), new("66",  "TH"),
            new("228", "TG"), new("690", "TK"), new("676", "TO"), new("1",   "TT"),
            new("216", "TN"), new("90",  "TR"), new("993", "TM"), new("1",   "TC"),
            new("688", "TV"), new("256", "UG"), new("380", "UA"), new("971", "AE"),
            new("44",  "GB"), new("1",   "US"), new("598", "UY"), new("998", "UZ"),
            new("678", "VU"), new("379", "VA"), new("58",  "VE"), new("84",  "VN"),
            new("1",   "VG"), new("1",   "VI"), new("681", "WF"), new("967", "YE"),
            new("260", "ZM"), new("263", "ZW")
        };

            // Try to match the longest possible country code first
            foreach (var kvp in countryCodes.OrderByDescending(k => k.Key.Length))
            {
                if (digitsOnly.StartsWith(kvp.Key))
                {
                    // Remove the code and any leading zeros
                    var numberWithoutCode = digitsOnly.Substring(kvp.Key.Length).TrimStart('0');
                    return (numberWithoutCode, kvp.Value);
                }
            }

            // No country code found
            return (digitsOnly.TrimStart('0'), string.Empty);
        }

        /// <summary>
        /// Searches for a Salesforce account by phone/email/name via a custom Apex REST endpoint.
        /// </summary>
        public async Task<(bool Exists, string AccountId,string OwnerId)> SearchSalesforceAccount(
    string phone, string email, string fullName)
        {
            try
            {
                var accessToken = _uniService1.GetAccessToken();

                // Extract country code and cleaned phone
                var (phoneWithoutCountryCode, countryIso) = ExtractCountryCode(phone);

                var client = _httpClientFactory.CreateClient();
                var request = new HttpRequestMessage(
                    System.Net.Http.HttpMethod.Post,
                    $"{_salesforceSettings.Value.ApexRestBaseUrl}/SearchMasterAccount/v1/");

                // Set the Authorization header on the request
                request.Headers.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                // Prepare JSON payload
                var payload = new
                {
                    MobilePhone = phoneWithoutCountryCode,
                    Email = email,
                    FullName = fullName
                };
                var json = Newtonsoft.Json.JsonConvert.SerializeObject(payload);

                // Create content and set its Content-Type header
                var content = new StringContent(json, Encoding.UTF8);
                content.Headers.ContentType =
                    new System.Net.Http.Headers.MediaTypeHeaderValue("application/json");

                request.Content = content;

                // Send and ensure success
                var response = await client.SendAsync(request);
                response.EnsureSuccessStatusCode();

                var responseBody = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Salesforce API Response: {responseBody}");

                // Deserialize
                var result = Newtonsoft.Json.JsonConvert
                    .DeserializeObject<SalesforceSearchResponse>(responseBody);

                if (result?.Data == null || result.Data.Count == 0)
                    return (false, null,null);

                // Collect unique account IDs
                var uniqueIds = result.Data
                    .Where(d => !string.IsNullOrEmpty(d.AccountId))
                    .Select(d => d.AccountId)
                    .Distinct();

                var accountIds = uniqueIds.Any()
                    ? string.Join(",", uniqueIds)
                    : null;
                var uniqueIds1 = result.Data
                  .Where(d => !string.IsNullOrEmpty(d.OwnerId))
                  .Select(d => d.OwnerId)
                  .Distinct();

                var accountIds1 = uniqueIds1.Any()
                    ? string.Join(",", uniqueIds1)
                    : null;

                return (accountIds != null, accountIds, accountIds1);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in SearchSalesforceAccount: {ex}");
                return (false, null,null);
            }
        }

    

        public class SalesforceSearchResponse
        {
            public bool Status { get; set; }
            public string Message { get; set; }
            public string Language { get; set; }
            public List<SalesforceAccount> Data { get; set; }
            public int Code { get; set; }
        }

        public class SalesforceAccount
        {
            public string MobilePhone { get; set; }
            public bool IsMaster { get; set; }
            public string FullName { get; set; }
            public string Email { get; set; }
            public string AccountId { get; set; }
            public string? OwnerId { get; internal set; }
        }

        async Task<string> insertlead(string FirstName, string LastName, string PersonMobilePhone, string Whatsapp_Number__pc, string PersonEmail, string Citizenship__pc, string Country_of_Residence__pc, string Person_Type_EDIS__pc, string EDIS_Name__pc, string EDIS_Code__pc, string utm_source__pc, string utm_medium__pc, string utm_campaign__pc, string utm_term__pc, string utm_content__pc, string Description, string uni, string major, string care, bool isholand, string Preferred_Degree__pc, string Preferred_Language__pc, string Where_did_you_hear_about_us__c, string Address__pc, string PersonGenderIdentity, string ownerid, string Branch__c, string Destination_Country__pc, string source,string Password__pc)
        {
            if (Destination_Country__pc == "Not found")
            {
                Destination_Country__pc = null;
            }
            if (Citizenship__pc == "Not found")
            {
                Citizenship__pc = null;
            }
            string sObject = "Account";
            object body = new object();


            body = new
            {
                Destination_Country__pc = Destination_Country__pc,
                Branch__c = Branch__c,
                PersonGenderIdentity = PersonGenderIdentity,

                //  Where_did_you_hear_about_us__c = Where_did_you_hear_about_us__c,
                //Preferred_Degree__pc= Preferred_Degree__pc,
                //Preferred_Language__pc = Preferred_Language__pc,

                PersonMobilePhone = PersonMobilePhone,
                Password__pc= Password__pc,

                FirstName = FirstName,
                LastName = LastName,
                Contact_Source__pc = source,
                Whatsapp_Number__pc = Whatsapp_Number__pc,
                RecordTypeId = "0128d000000Z2vvAAC",
                OwnerId = ownerid,
                Person_Type_EDIS__pc = Person_Type_EDIS__pc,
                utm_source__pc = utm_source__pc,
                EDIS_Code__pc = EDIS_Code__pc,
                EDIS_Name__pc = EDIS_Name__pc,
                utm_medium__pc = utm_medium__pc,
                utm_campaign__pc = utm_campaign__pc,
                utm_term__pc = utm_term__pc,
                utm_content__pc = utm_content__pc,
                PersonEmail = PersonEmail,
                Citizenship__pc = Citizenship__pc,
                Country_of_Residence__pc = Country_of_Residence__pc,

                LastModifiedById = "0054L000001IJ70QAG",
                CreatedById = "0054L000001IJ70QAG"


            };



            //update
            return await _uniService.Insert(sObject, body);
        }
        List<string> blocklist = new List<string>() { "كس", "شراميط", "شرموط", "حرامي", "نصاب", "اتووووف", "امهاتكم" };
        
        private async Task<Student> GetStudentByReferralCode(string referralCode)
        {
            if (string.IsNullOrWhiteSpace(referralCode))
                return null;
                
            return await _context.Students
                .FirstOrDefaultAsync(s => s.RefCode == referralCode);
        }
        [HttpPost("register")]
        public async Task<IActionResult> Register([FromBody] RegisterRequest model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Check if phone number already exists
            var existingStudent = await _context.Students.FirstOrDefaultAsync(s => s.PhoneNumber == model.PhoneNumber);
            var existingStudentusr = await _context.Users.FirstOrDefaultAsync(s => s.PhoneNumber == model.PhoneNumber);
            if (existingStudent != null && existingStudentusr != null)
            {
                return BadRequest(new { message = "Phone number already exists." });
            }

            // Check if email already exists
            var existingUserByEmail = await _userManager.FindByEmailAsync(model.Email);
            if (existingUserByEmail != null)
            {
                return BadRequest(new { message = "Email already exists." });
            }

            // Validate DateOfBirth
            if (!model.DateOfBirth.HasValue)
            {
                ModelState.AddModelError(nameof(model.DateOfBirth), "Date of Birth is required.");
                return BadRequest(ModelState);
            }
            var user = new ApplicationUser
            {
                UserName = model.Email, // or model.UserName if available
                Email = model.Email,
                PhoneNumber = model.PhoneNumber,
                FirstName = model.FirstName,
                LastName = model.LastName

            ,
                Gender = model.Gender,

                CountryOfResidence = model.CountryOfResidence,
                Citizenship = model.Citizenship
            };


            var result = await _userManager.CreateAsync(user, model.Password);
            if (!result.Succeeded)
            {
                foreach (var error in result.Errors)
                {
                    ModelState.AddModelError(string.Empty, error.Description);
                }
                return BadRequest(ModelState);
            }

            // Assign the 'Student' role to the newly registered user
            await _userManager.AddToRoleAsync(user, "Student");
            string source = "Web";
            if (model.isholand)
            {
                source = "Holland Test";
            }
            var utm_source__pc = "";
            var utm_medium__pc = "";
            var utm_campaign__pc = "";
            var utm_term__pc = "";
            var utm_content__pc = "";
            var langsite = model.lang;
            DateTime xy1 = DateTime.Now;
            string ownerid = "0054L000001IJ70QAG";
            string branch = "United Main";

            if (utm_source__pc == "" || utm_source__pc == "undefined" || utm_source__pc == null)
            {
                utm_source__pc = "Organic";
            }
            if (utm_medium__pc == "" || utm_medium__pc == "undefined" || utm_medium__pc == null)
            {
                if (langsite == "fa")
                {
                    utm_medium__pc = "FA_landing_page";
                    ownerid = "0058d000007V1b0AAC";
                    branch = "Persian";
                }
                else if (langsite == "fr")
                {
                    utm_medium__pc = "FR_landing_page";
                    ownerid = "0058d000007SmI6AAK";
                    branch = "Morocco";
                }
                else if (langsite == "tr")
                {
                    utm_medium__pc = "TR_landing_page";
                    branch = "United English Main";
                }
                else if (langsite == "en")
                {
                    utm_medium__pc = "EN_landing_page";
                    branch = "United English Main";
                }
                else if (langsite == "ru")
                {
                    utm_medium__pc = "RU_landing_page";
                    ownerid = "0058d000007V1b0AAC";
                    branch = "Russian";
                }
                else { utm_medium__pc = "AR_landing_page"; }

            }
            if (utm_campaign__pc == "" || utm_campaign__pc == "undefined" || utm_campaign__pc == null)
            {
                utm_campaign__pc = "Organic";
            }
            if (utm_term__pc == "" || utm_term__pc == "undefined" || utm_term__pc == null)
            {
                utm_term__pc = xy1.ToString("yyyy") + xy1.ToString("MM") + xy1.ToString("dd");
            }
            if (utm_content__pc == "" || utm_content__pc == "undefined" || utm_content__pc == null)
            {
                utm_content__pc = "landing_page";
            }
            if (langsite == "fa")
            {

                ownerid = "0058d000007V1b0AAC";
                branch = "Persian";
            }
            else if (langsite == "fr")
            {

                ownerid = "0058d000007SmI6AAK";
                branch = "Morocco";
            }

            else if (langsite == "ru")
            {

                ownerid = "0058d000007V1b0AAC";
                branch = "Russian";
            }
            else if (langsite == "tr")
            {

                branch = "United English Main";
            }
            else if (langsite == "en")
            {

                branch = "United English Main";
            }
            string[] words = model.FirstName.Split(' ');
            string[] words1 = model.LastName.Split(' ');
            foreach (string word in words)
            {
                if (blocklist.Contains(word))
                {

                    return BadRequest(new { message = "حدث خطأ يرجى الانتياه" });

                }
            }
            foreach (string word in words1)
            {
                if (blocklist.Contains(word))
                {


                    return BadRequest(new { message = "حدث خطأ يرجى الانتياه" });
                }
            }
            string salceid = string.Empty;
            string ownerid1 = string.Empty;
            var pass = _uniService.hashpassword(model.Password);
            var (accountExists, accountId, OwnerId) = await SearchSalesforceAccount(model.PhoneNumber, model.Email, $"{model.FirstName} {model.LastName}");
            if (accountExists)
            {
                salceid = accountId;
                ownerid1 = OwnerId;
                if (!accountId.Contains(","))
                {
                    _uniService.updatepassword(accountId, pass);
                }
                var existingCodes = _context.SmsVerifications
.Where(v => v.PhoneNumber == model.PhoneNumber);

                _context.SmsVerifications.RemoveRange(existingCodes);
                // You can use accountId here if needed
            }
            else
            {
                var responseFromServer = insertlead(model.FirstName, model.LastName, model.PhoneNumber, model.PhoneNumber, model.Email, _uniService.Countryen(model.Citizenship), _uniService.Countryen(model.CountryOfResidence), null, null, user.Id, utm_source__pc, utm_medium__pc, utm_campaign__pc, utm_term__pc, utm_content__pc, "10", null, null, null, model.isholand, null, null, null, null, model.Gender, ownerid, branch, "Türkiye", source,pass);
                JObject jsonObject = JObject.Parse(responseFromServer.Result.ToString());

                 salceid = (string)jsonObject["id"];
                var existingCodes = _context.SmsVerifications
.Where(v => v.PhoneNumber == model.PhoneNumber);

                _context.SmsVerifications.RemoveRange(existingCodes);


            }

            // Process referral code if provided
            if (!string.IsNullOrEmpty(model.ReferralCode))
            {
                var referringStudent = await GetStudentByReferralCode(model.ReferralCode);
                if (referringStudent == null)
                {
                    return BadRequest(new { message = "Invalid referral code." });
                }

                // Create referral record
                var referral = new Referral
                {
                    ReferralCode = model.ReferralCode,
                    ReferrerId = referringStudent.Id,
                    ReferredStudentId = user.Id,
                    ReferralDate = DateTime.UtcNow
                };

                _context.Referrals.Add(referral);
                await _context.SaveChangesAsync();
            }
            if (string.IsNullOrEmpty(ownerid1))
            {
                ownerid1 = ownerid;
            }
            if (ownerid1.Contains(","))
            {
                var student = new Student
                {
                    CrmId = salceid,
                    Id = user.Id,
                    FirstName = model.FirstName,
                    LastName = model.LastName,
                    Email = model.Email,
                    PhoneNumber = model.PhoneNumber,
                    Gender = model.Gender,
                    CountryOfResidence = model.CountryOfResidence,
                    Citizenship = model.Citizenship,
                    DateOfBirth = model.DateOfBirth.Value,
                    RefCode = user.Id?.Substring(0, 8) ?? string.Empty,
                    guid = Guid.NewGuid().ToString(),
                    lang = model.lang,
                    year = DateTime.Now.Year.ToString("yyyy"),
                 


                };

                _context.Students.Add(student);
                await _context.SaveChangesAsync();
            }
            else
            {
                int owerner = _context.Consultants.Where(a => a.SalesforceId == ownerid1).Select(a => a.Id).FirstOrDefault();
                // Create student record
                var student = new Student
                {
                    CrmId = salceid,
                    Id = user.Id,
                    FirstName = model.FirstName,
                    LastName = model.LastName,
                    Email = model.Email,
                    PhoneNumber = model.PhoneNumber,
                    Gender = model.Gender,
                    CountryOfResidence = model.CountryOfResidence,
                    Citizenship = model.Citizenship,
                    DateOfBirth = model.DateOfBirth.Value,
                    RefCode = user.Id?.Substring(0, 8) ?? string.Empty,
                    guid = Guid.NewGuid().ToString(),
                    lang = model.lang,
                    year = DateTime.Now.Year.ToString("yyyy"),
                    ConsultantId = owerner


                };

                _context.Students.Add(student);
                await _context.SaveChangesAsync();
            }
            return Ok(new { message = "User registered successfully" });
        }
  
        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginRequest model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            ApplicationUser user;
            if (Regex.IsMatch(model.EmailOrPhone, @"^[^@\s]+@[^@\s]+\.[^@\s]+$"))
            {
                user = await _userManager.FindByEmailAsync(model.EmailOrPhone);
            }
            else
            {
                user = await _userManager.Users.FirstOrDefaultAsync(u => u.PhoneNumber == model.EmailOrPhone);
            }

            if (user == null)
            {
                return Unauthorized(new { message = "Invalid login attempt" });
            }

            var result = await _signInManager.CheckPasswordSignInAsync(user, model.Password, false);

            if (!result.Succeeded)
            {
                return Unauthorized(new { message = "Invalid login attempt" });
            }

            // Get student data with consultant information
            var student = await _context.Students
                .AsNoTracking()
                .Include(s => s.Consultant) // Include consultant data
                .Where(s => s.Id == user.Id)

                .FirstOrDefaultAsync();

           
        // Generate JWT token for the new user
        var (token, claims) = GenerateJwtToken(user, student);

            return Ok(new AuthResponse
            {
                Token = token,
                UserId = user.Id,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                ImageUrl = string.Empty, // Will be set when image is uploaded
                Expiration = DateTime.Now.AddDays(7),
                Claims = claims,
                RefCode = student.RefCode,
                Point = 0
            });
        }
        public class phonecheck
        {
            [Required(ErrorMessage = "Email or phone number is required")]
            public string EmailOrPhone { get; set; }


        }

        // Google OAuth models
        public class GoogleTokenResponse
        {
            public string AccessToken { get; set; } = string.Empty;
            public string TokenType { get; set; } = string.Empty;
            public int ExpiresIn { get; set; }
            public string RefreshToken { get; set; } = string.Empty;
            public string Scope { get; set; } = string.Empty;
        }

        public class GoogleUserInfo
        {
            public string Id { get; set; } = string.Empty;
            public string Email { get; set; } = string.Empty;
            public bool VerifiedEmail { get; set; }
            public string Name { get; set; } = string.Empty;
            public string GivenName { get; set; } = string.Empty;
            public string FamilyName { get; set; } = string.Empty;
            public string Picture { get; set; } = string.Empty;
            public string Locale { get; set; } = string.Empty;
        }

        [HttpPost("checkphonenumber")]
        public async Task<IActionResult> checkphonenumber([FromBody] phonecheck model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            ApplicationUser user;
            // Check if phone number already exists
            var existingStudent = await _context.Students.FirstOrDefaultAsync(s => s.PhoneNumber == model.EmailOrPhone);
            var existingStudentusr = await _context.Users.FirstOrDefaultAsync(s => s.PhoneNumber == model.EmailOrPhone);
            if (existingStudent != null && existingStudentusr != null)
            {
                return BadRequest(new { message = "Phone number already exists." });
            }
            var existingCodes = _context.SmsVerifications
    .Where(v => v.PhoneNumber == model.EmailOrPhone);

            _context.SmsVerifications.RemoveRange(existingCodes);
            var code = new Random().Next(100000, 999999).ToString();
            var record = new SmsVerification
            {
                PhoneNumber = model.EmailOrPhone,
                Code = code,
                CreatedAt = DateTime.UtcNow,
                IsVerified = false
            };

            _context.SmsVerifications.Add(record);
            await _context.SaveChangesAsync();

            return Ok();
        }
        [HttpPost("checkemail")]
        public async Task<IActionResult> checkemail([FromBody] phonecheck model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            ApplicationUser user;
            // Check if phone number already exists
            var existingStudent = await _context.Students.FirstOrDefaultAsync(s => s.Email == model.EmailOrPhone);
            var existingStudentusr = await _context.Users.FirstOrDefaultAsync(s => s.Email == model.EmailOrPhone);
            if (existingStudent != null && existingStudentusr != null)
            {
                return BadRequest(new { message = "Email already exists." });
            }




            return Ok();
        }
        private (string token, Dictionary<string, string> claims) GenerateJwtToken(ApplicationUser user, dynamic student)
        {
            var claimsList = new List<Claim>
            {
                new Claim(JwtRegisteredClaimNames.Sub, user.Email ?? string.Empty),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(ClaimTypes.NameIdentifier, user.Id),
                new Claim(ClaimTypes.Name, user.UserName ?? string.Empty),
                new Claim("FirstName", user.FirstName ?? string.Empty),
                new Claim("LastName", user.LastName ?? string.Empty),
                new Claim("Email", user.Email ?? string.Empty)
            };

            // Add student claims if student is not null
            if (student != null)
            {
                // Basic student info
                var studentClaims = new List<Claim>
{
    new Claim("StudentId", $"{student?.Id ?? ""}"),
    new Claim("ImageUrl", $"{student?.ImageURL ?? ""}"),
    new Claim("CrmId", $"{student?.CrmId ?? ""}"),
    new Claim("PhoneNumber", $"{student?.PhoneNumber ?? ""}"),
    new Claim("DateOfBirth", student?.DateOfBirth?.ToString("yyyy-MM-dd") ?? string.Empty),
    new Claim("Citizenship", $"{student?.Citizenship ?? ""}"),
    new Claim("Gender", $"{student?.Gender ?? ""}"),
    new Claim("PassportNumber", $"{student?.PassportNumber ?? ""}"),
    new Claim("CurrentStage", $"{student?.CurrentStage ?? ""}"),
    new Claim("CountryOfResidence", $"{student?.CountryOfResidence ?? ""}"),
    new Claim("TCKimlikNumber", $"{student?.TCKimlikNumber ?? ""}"),
    new Claim("FatherName", $"{student?.FatherName ?? ""}"),
    new Claim("MotherName", $"{student?.Mothername ?? ""}"),
    new Claim("RegistrationType", $"{student?.RegistrationType ?? ""}"),
    new Claim("SecondarySchoolCountry", $"{student?.SecondarySchoolCountry ?? ""}"),
    new Claim("SecondarySchoolName", $"{student?.SecondarySchoolName ?? ""}"),
    new Claim("SchoolOrUniversityName", $"{student?.SchoolOrUniversityName ?? ""}"),
    new Claim("DestinationCountry", $"{student?.DestinationCountry ?? ""}"),
    new Claim("DegreeInterest", $"{student?.DegreeInterest ?? ""}"),
    new Claim("Language", $"{student?.Language ?? ""}"),
    new Claim("FieldOfStudyInterest", $"{student?.FieldOfStudyInterest ?? ""}"),
    new Claim("InterestedUniversities", $"{student?.InterestedUniversities ?? ""}"),

    new Claim("RefCode", $"{student?.RefCode ?? ""}"),
    new Claim("Point", $"{student?.Point ?? 0}"),
    new Claim("AddressCountry", $"{student?.AddressCountry ?? ""}"),
    new Claim("AddressCity", $"{student?.AddressCity ?? ""}"),
    new Claim("AddressStreet", $"{student?.AddressStreet?? ""}"),
};
                var consultant = student?.Consultant;
                if (consultant != null)
                {
                    var consultantClaims = new List<Claim>
                {
                    new Claim("ConsultantId", $"{consultant?.Id ?? ""}"),
                    new Claim("ConsultantFirstName", $"{consultant?.FirstName ?? ""}"),
                    new Claim("ConsultantLastName", $"{consultant?.LastName ?? ""}"),
                    new Claim("ConsultantEmail", $"{consultant?.Email ?? ""}"),
                    new Claim("ConsultantPhoneNumber", $"{consultant?.PhoneNumber ?? ""}"),
                        new Claim("ConsultantImageURL", $"{consultant?.ImageURL ?? ""}"),
                              new Claim("ConsultantDescription", $"{consultant?.Description ?? ""}")
                };

                    studentClaims.AddRange(consultantClaims);
                }

                // Add all non-empty claims to the claims list
                foreach (var claim in studentClaims)
                {
                    if (!string.IsNullOrEmpty(claim.Value))
                    {
                        claimsList.Add(claim);
                    }
                }
            }

            var claimsDict = claimsList.ToDictionary(c => c.Type, c => c.Value);

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"]));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
            var expires = DateTime.Now.AddDays(30);

            var token = new JwtSecurityToken(
                _configuration["Jwt:Issuer"],
                _configuration["Jwt:Audience"],
                claimsList,
                expires: expires,
                signingCredentials: creds
            );

            return (new JwtSecurityTokenHandler().WriteToken(token), claimsDict);
        }

        public class GenerateTokenRequest
        {
            [Required(ErrorMessage = "Phone number is required")]
            public string PhoneNumber { get; set; }
        }

        public class ResetPasswordRequest
        {
            [Required(ErrorMessage = "Phone number is required")]
            public string PhoneNumber { get; set; }

            [Required(ErrorMessage = "Token is required")]
            public string Token { get; set; }

            [Required(ErrorMessage = "New password is required")]
            [StringLength(100, ErrorMessage = "The {0} must be at least {2} and at max {1} characters long.", MinimumLength = 6)]
            public string NewPassword { get; set; }
        }


        [HttpPost("generate-reset-token")]
        public async Task<IActionResult> GeneratePasswordResetToken([FromBody] GenerateTokenRequest model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Find user by phone number
            var user = await _userManager.Users.FirstOrDefaultAsync(u => u.PhoneNumber == model.PhoneNumber);
            if (user == null)
            {
                // Don't reveal that the user doesn't exist
                return Ok(new { message = "If your phone number is registered, you will receive a password reset link." });
            }

            // Generate password reset token
            var token = await _userManager.GeneratePasswordResetTokenAsync(user);

            // In a real application, you would send this token via SMS or email
            // For this example, we'll return it in the response
            // IMPORTANT: In production, never return the token in the response

            return Ok(new
            {
                message = "Password reset token generated successfully",
                // In production, remove the token from the response and send it via SMS/Email
                token = token
            });
        }

        [HttpPost("reset-password")]
        public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordRequest model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Find user by phone number
            var user = await _userManager.Users.FirstOrDefaultAsync(u => u.PhoneNumber == model.PhoneNumber);
            if (user == null)
            {
                // Don't reveal that the user doesn't exist
                return Ok(new { message = "If your phone number is registered, you will receive a password reset link." });
            }
            var pass = _uniService.hashpassword(model.NewPassword);
            // Reset the password
            var result = await _userManager.ResetPasswordAsync(user, model.Token, model.NewPassword);
            _uniService.updatepassword(user.CrmId, pass);

            if (!result.Succeeded)
            {
                // Return the first error
                return BadRequest(new { message = result.Errors.FirstOrDefault()?.Description ?? "Failed to reset password." });
            }

            return Ok(new { message = "Password has been reset successfully." });
        }
    }
}