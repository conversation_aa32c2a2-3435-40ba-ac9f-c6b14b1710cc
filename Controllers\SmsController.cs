using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using StudentManagementAPI.Data;
using StudentManagementAPI.Models;
using System.Net.Http;
using System.Text;
using Twilio;
using Twilio.Rest.Api.V2010.Account;

namespace StudentManagementAPI.Controllers
{
    [ApiController]
    [Route("api/sms")]
    public class SmsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ILanguageService _languageService;
        private readonly ILogger<SmsController> _logger;

        public SmsController(
            ApplicationDbContext context, 
            IHttpClientFactory httpClientFactory, 
            ILanguageService languageService,
            ILogger<SmsController> logger)
        {
            _context = context;
            _httpClientFactory = httpClientFactory;
            _languageService = languageService;
            _logger = logger;
        }

        private async Task<bool> VerifyCaptcha(string token)
        {
            var secret = "6LdD3KQrAAAAAPl_nSfHJlD1sVTv8wXprSxB61_q";
            var client = _httpClientFactory.CreateClient();
            var response = await client.PostAsync($"https://www.google.com/recaptcha/api/siteverify?secret={secret}&response={token}", null);
            var json = await response.Content.ReadAsStringAsync();
            dynamic result = JsonConvert.DeserializeObject(json);
            return result.success == true && result.score > 0.5;
        }

        [HttpPost("send-code")]
        public async Task<IActionResult> SendCode([FromBody] SmsRequest request)
        {
            try
            {
                // Input validation
                if (request == null)
                    return BadRequest("Request cannot be null");

                if (string.IsNullOrWhiteSpace(request.PhoneNumber) || string.IsNullOrWhiteSpace(request.Country))
                    return BadRequest("Phone number and country are required.");

                // Clean phone number (remove any non-digit characters except +)
                request.PhoneNumber = new string(request.PhoneNumber.Where(c => char.IsDigit(c) || c == '+').ToArray());

                // Validate phone number format
                if (request.PhoneNumber.Length < 10 || request.PhoneNumber.Length > 15)
                    return BadRequest("Invalid phone number format");

                // Clean up old verification codes for this number
                var existingCodes = _context.SmsVerifications
                    .Where(v => v.PhoneNumber == request.PhoneNumber);

                if (existingCodes.Any())
                {
                    _context.SmsVerifications.RemoveRange(existingCodes);
                    await _context.SaveChangesAsync();
                }

                // Verify CAPTCHA
                if (string.IsNullOrWhiteSpace(request.CaptchaToken))
                    return BadRequest("Captcha token is required");

                var captchaResult = await VerifyCaptcha(request.CaptchaToken);
                if (!captchaResult)
                    return BadRequest("Captcha verification failed. Please try again.");

                // Check rate limiting
                var recent = await _context.SmsVerifications
                    .Where(v => v.PhoneNumber == request.PhoneNumber)
                    .OrderByDescending(v => v.CreatedAt)
                    .FirstOrDefaultAsync();

                if (recent != null && (DateTime.UtcNow - recent.CreatedAt).TotalSeconds < 60)
                {
                    var secondsLeft = 60 - (DateTime.UtcNow - recent.CreatedAt).TotalSeconds;
                    return BadRequest(new { 
                        Message = $"Please wait {Math.Ceiling(secondsLeft)} seconds before requesting a new code.",
                        RetryAfter = Math.Ceiling(secondsLeft)
                    });
                }

                // Generate a secure random code
                var code = new Random().Next(100000, 999999).ToString("D6");
                bool isSent = false;
                string provider = string.Empty;

                try
                {
                    // Determine which SMS provider to use based on country
                    switch (request.Country.ToLower())
                    {
                        case "turkey":
                            provider = "TurkeySMS";
                            isSent = await SendViaTurkeySms(request.PhoneNumber, code, request.Language ?? "en");
                            break;
                        case "iran":
                        case "china":
                            provider = "Twilio";
                            isSent = await SendViaTwilio(request.PhoneNumber, code);
                            break;
                        default:
                            return BadRequest(new { Message = $"SMS service is not available for {request.Country}." });
                    }

                    if (!isSent)
                    {
                        _logger.LogError($"Failed to send SMS via {provider} to {request.PhoneNumber}");
                        return StatusCode(500, new { Message = "Failed to send verification code. Please try again later." });
                    }

                    // Save the verification code - only using columns that exist in the database
                    var record = new SmsVerification
                    {
                        PhoneNumber = request.PhoneNumber,
                        Code = code,
                        CreatedAt = DateTime.UtcNow,
                        IsVerified = false
                    };

                    _context.SmsVerifications.Add(record);
                    await _context.SaveChangesAsync();
                    
                    // Log additional info that's not saved to the database
                    _logger.LogInformation($"SMS sent to {request.PhoneNumber} for country {request.Country} using {provider}");

                    // Don't expose the code in the response
                    return Ok(new { 
                        Message = "Verification code sent successfully.",
                        // For development/testing only - remove in production
                        // DebugInfo = new { code = code } 
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error sending SMS to {request.PhoneNumber} via {provider}");
                    return StatusCode(500, new { 
                        Message = "An error occurred while sending the verification code.",
                        // Include more details in development
                        Details = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development" ? ex.Message : null
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error in SendCode");
                return StatusCode(500, new { Message = "An unexpected error occurred. Please try again later." });
            }
        }

        [HttpPost("verify-code")]
        public async Task<IActionResult> VerifyCode([FromBody] SmsVerificationRequest request)
        {
            try
            {
                // Find the most recent unverified code for this phone number
                var record = await _context.SmsVerifications
                    .Where(v => v.PhoneNumber == request.PhoneNumber && !v.IsVerified)
                    .OrderByDescending(v => v.CreatedAt)
                    .FirstOrDefaultAsync();

                if (record == null)
                    return BadRequest(new { Message = "No verification code found or already verified." });

                // Check if code is expired (10 minutes)
                if ((DateTime.UtcNow - record.CreatedAt).TotalMinutes > 10)
                    return BadRequest(new { Message = "Verification code has expired. Please request a new one." });

                // Verify the code
                if (record.Code != request.Code)
                {
                    _logger.LogWarning($"Invalid verification code for {request.PhoneNumber}");
                    return BadRequest(new { Message = "Invalid verification code." });
                }

                // Mark as verified
                record.IsVerified = true;
                await _context.SaveChangesAsync();

                _logger.LogInformation($"Phone number {request.PhoneNumber} verified successfully");
                return Ok(new { Message = "Phone number verified successfully." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error verifying code for {request?.PhoneNumber}");
                return StatusCode(500, new { Message = "An error occurred while verifying the code. Please try again." });
            }
        }

        private Dictionary<string, string> GetSmsTemplates(string language, string code = "")
        {
            // Default to English if language not supported
            language = _languageService.ValidateLanguage(language);
            
            var templates = new Dictionary<string, string>();
            
            switch (language)
            {
                case "ar":
                    templates["verification"] = "كود التحقق الخاص بك هو: {0}";
                    templates["welcome"] = "مرحباً بك في خدمتنا! تم إنشاء حسابك.";
                    templates["password_reset"] = "تم إعادة تعيين كلمة المرور الخاصة بك. إذا لم تطلب ذلك، يرجى الاتصال بالدعم.";
                    break;
                    
                case "ru":
                    templates["verification"] = "Ваш код подтверждения: {0}";
                    templates["welcome"] = "Добро пожаловать в наш сервис! Ваш аккаунт создан.";
                    templates["password_reset"] = "Ваш пароль был сброшен. Если вы не запрашивали это, пожалуйста, свяжитесь со службой поддержки.";
                    break;
                    
                case "fa":
                    templates["verification"] = "کد تأیید شما: {0}";
                    templates["welcome"] = "به سرویس ما خوش آمدید! حساب کاربری شما ایجاد شد.";
                    templates["password_reset"] = "رمز عبور شما بازنشانی شد. اگر شما این درخواست را ندادید، لطفاً با پشتیبانی تماس بگیرید.";
                    break;
                    
                default: // English
                    templates["verification"] = "Your verification code is: {0}";
                    templates["welcome"] = "Welcome to our service! Your account has been created.";
                    templates["password_reset"] = "Your password has been reset. If you didn't request this, please contact support.";
                    break;
            }
            
            return templates;
        }

        private async Task<bool> SendViaTurkeySms(string phoneNumber, string code, string language = "en", string messageType = "verification")
        {
            var client = _httpClientFactory.CreateClient();
            
            try
            {
                // Get the appropriate message template based on language and message type
                var templates = GetSmsTemplates(language);
                if (!templates.TryGetValue(messageType.ToLower(), out var template))
                {
                    template = templates["verification"]; // Fallback to verification template
                }

                // Format the message with the code if needed
                var message = string.Format(template, code);

                // Build query parameters
                var queryParams = new Dictionary<string, string>
                {
                    { "api_key", "08bc4c42e0aa5a868adae505403195555" },
                    { "mobile", phoneNumber },
                    { "title", "UnitedGroup" },  // Your sender ID
                    { "text", message },
                    { "report", "1" },  // Request delivery report
                    { "lang", language == "tr" ? "1" : "2" },  // 1: Turkish, 2: English
                    { "response_type", "json" },
                    { "content_type", "1" }  // 1: Normal SMS, 2: Flash SMS
                };

                // Build query string
                var queryString = string.Join("&", queryParams
                    .Select(kvp => $"{Uri.EscapeDataString(kvp.Key)}={Uri.EscapeDataString(kvp.Value)}"));

                var url = $"https://turkeysms.com.tr/api/v3/get/get.php?{queryString}";
                
                _logger.LogInformation($"Sending SMS to {phoneNumber} via TurkeySMS API");
                
                // Add timeout to prevent hanging
                client.Timeout = TimeSpan.FromSeconds(30);

                // Send GET request
                var response = await client.GetAsync(url);
                var responseBody = await response.Content.ReadAsStringAsync();

                // Log the raw response for debugging
                _logger.LogInformation($"SMS API Response: {responseBody}");

                // Check if the response is HTML (which would indicate an error)
                if (responseBody.TrimStart().StartsWith("<") && responseBody.Contains("<html"))
                {
                    _logger.LogError("Received HTML response instead of JSON from SMS provider");
                    return false;
                }

                // Try to parse the JSON response
                try
                {
                    var result = JsonConvert.DeserializeObject<Dictionary<string, object>>(responseBody);
                    if (result == null)
                    {
                        _logger.LogError("Failed to deserialize SMS API response");
                        return false;
                    }

                    // Check if the response indicates success based on the API's response format
                    bool isSuccess = (result.TryGetValue("result", out var resultValue) && resultValue is bool boolResult && boolResult) ||
                                   (result.TryGetValue("result_code", out var resultCode) && 
                                    string.Equals(resultCode?.ToString(), "TS-1024", StringComparison.OrdinalIgnoreCase));
                    
                    if (isSuccess)
                    {
                        _logger.LogInformation($"SMS sent successfully. SMS ID: {result.GetValueOrDefault("sms_id")}");
                    }
                    else
                    {
                        var errorMessage = result.GetValueOrDefault("result_message")?.ToString() ?? "Unknown error";
                        _logger.LogError($"SMS API returned error: {errorMessage}");
                        
                        // Log additional debug info if available
                        if (result.TryGetValue("result_code", out var errorCode))
                        {
                            _logger.LogError($"Error Code: {errorCode}");
                        }
                    }
                    
                    return isSuccess;
                }
                catch (JsonException ex)
                {
                    _logger.LogError(ex, "Failed to parse SMS API response");
                    return false;
                }
            }
            catch (Exception ex)
            {
                // Log the error
                _logger.LogError(ex, "Error sending SMS via TurkeySMS");
                return false;
            }
        }

        private async Task<bool> SendViaTwilio(string phoneNumber, string code)
        {
            try
            {
                const string accountSid = "**********************************";
                const string authToken = "55305466061ea1437606deb77d94a8db";
                const string fromNumber = "YOUR_TWILIO_PHONE_NUMBER";

                TwilioClient.Init(accountSid, authToken);

                var message = await MessageResource.CreateAsync(
                    body: $"Your verification code is: {code}",
                    from: new Twilio.Types.PhoneNumber(fromNumber),
                    to: new Twilio.Types.PhoneNumber(phoneNumber)
                );

                return message.ErrorCode == null;
            }
            catch
            {
                return false;
            }
        }
    }
    public class SmsRequest
    {
        public string PhoneNumber { get; set; }
        public string Country { get; set; }
        public string Language { get; set; } = "en"; // Default to English
        public string CaptchaToken { get; set; } // for CAPTCHA protection
    }

    // SmsVerificationRequest.cs
    public class SmsVerificationRequest
    {
        public string PhoneNumber { get; set; }
        public string Code { get; set; }
    }

}
